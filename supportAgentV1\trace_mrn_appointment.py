"""
trace_mrn_appointment.py
Queries database by MRN and Appointment Number following the complete data flow
Usage:
    python trace_mrn_appointment.py <MRN> <AppointmentNo>
"""

import json
import sys
from datetime import datetime, timezone

import pandas as pd
import pyodbc


# ────────────── 0.  CONNECT ──────────────
conn = pyodbc.connect(
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=HAARLT0397;"
    "DATABASE=V811QA;"
    "Trusted_Connection=yes;"
    "Encrypt=yes;"
    "TrustServerCertificate=yes;"
)


# ────────────── 1.  HELPERS ──────────────
def fetch_df(cur, sql: str, params=()):
    cur.execute(sql, params)
    cols = [c[0] for c in cur.description]
    return pd.DataFrame.from_records(cur.fetchall(), columns=cols)


def build_mrn_appointment_json(mrn: str, appointment_no: str) -> dict:
    with conn.cursor() as cur:
        # 1. Get User by MRN
        user_df = fetch_df(
            cur,
            "SELECT * FROM Users WHERE MRN = ?",
            (mrn,),
        )
        if user_df.empty:
            raise ValueError(f"No user found for MRN: {mrn}")

        user = user_df.iloc[0].to_dict()
        user_id = user["UserID"]

        # 2. Get specific appointment
        appt_df = fetch_df(
            cur,
            "SELECT * FROM Appointments WHERE UserID = ? AND AppNo = ?",
            (user_id, appointment_no),
        )
        if appt_df.empty:
            raise ValueError(f"No appointment found for UserID: {user_id}, AppNo: {appointment_no}")

        appointment = appt_df.iloc[0].to_dict()
        appt_id = appointment.get("AppointmentId") or appointment.get("AppId") or appointment.get("ID")

        # 3. Get Appointments_Support records
        support_df = fetch_df(
            cur,
            "SELECT * FROM Appointments_Support WHERE AppointmentNo = ?",
            (appointment_no,),
        )

        # 4. Process support records following the exact hierarchy
        support_hierarchy = []
        
        for _, sup_row in support_df.iterrows():
            support_data = sup_row.to_dict()
            support_appt_id = support_data["AppointmentId"]

            # Get WorkflowAuditLog records for this support record
            audit_df = fetch_df(
                cur,
                "SELECT * FROM WorkflowAuditLog WHERE AppointmentId = ?",
                (support_appt_id,),
            )

            # Process each audit record with its KioskWorkflow and QuestionTemplate
            workflow_audit_hierarchy = []
            
            for _, audit_row in audit_df.iterrows():
                audit_data = audit_row.to_dict()
                kiosk_workflow_id = audit_data.get("KioskWorkflowId")
                
                if kiosk_workflow_id:
                    # Get KioskWorkflow for this specific audit
                    kiosk_df = fetch_df(
                        cur,
                        "SELECT * FROM KioskWorkflow WHERE KioskWorkflowId = ?",
                        (kiosk_workflow_id,),
                    )
                    
                    if not kiosk_df.empty:
                        kiosk_data = kiosk_df.iloc[0].to_dict()
                        question_template_id = kiosk_data.get("QuestionTemplateId")
                        
                        # Get QuestionTemplate for this KioskWorkflow
                        question_template_data = None
                        if question_template_id:
                            try:
                                template_df = fetch_df(
                                    cur,
                                    "SELECT * FROM QuestionsTemplate WHERE QuestionTempId = ?",
                                    (question_template_id,),
                                )
                                if not template_df.empty:
                                    question_template_data = template_df.iloc[0].to_dict()
                            except pyodbc.Error as e:
                                print(f"⚠️  QuestionsTemplate not accessible for ID {question_template_id}: {e}")
                        
                        # Build hierarchical structure for this audit
                        audit_data["KioskWorkflow"] = {
                            **kiosk_data,
                            "QuestionTemplate": question_template_data
                        }
                    else:
                        audit_data["KioskWorkflow"] = None
                else:
                    audit_data["KioskWorkflow"] = None
                
                workflow_audit_hierarchy.append(audit_data)
            
            # Add workflow audit hierarchy to support record
            support_data["WorkflowAuditLog"] = workflow_audit_hierarchy
            support_hierarchy.append(support_data)

        # 5. Get transaction-based data (TransactionNo branch)
        transaction_data = {}
        
        # OutBound_Document
        try:
            outbound_df = fetch_df(
                cur,
                "SELECT * FROM OutBound_Document WHERE transactionNo = ?",
                (appointment_no,),
            )
            transaction_data["OutboundDocuments"] = outbound_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  OutBound_Document table not accessible: {e}")
            transaction_data["OutboundDocuments"] = []

        # DocumentManagementDetails
        try:
            doc_mgmt_df = fetch_df(
                cur,
                "SELECT * FROM DocumentManagementDetails WHERE TransactionNo = ?",
                (appointment_no,),
            )
            transaction_data["DocumentManagementDetails"] = doc_mgmt_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  DocumentManagementDetails table not accessible: {e}")
            transaction_data["DocumentManagementDetails"] = []

        # APITransactions
        try:
            api_trans_df = fetch_df(
                cur,
                "SELECT * FROM APITransactions WHERE AppointmentNo = ? ORDER BY 1 DESC",
                (appointment_no,),
            )
            transaction_data["APITransactions"] = api_trans_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  APITransactions table not accessible: {e}")
            transaction_data["APITransactions"] = []

        # Notification_Actions_Queue
        try:
            notification_queue_df = fetch_df(
                cur,
                "SELECT * FROM Notification_Actions_Queue WHERE appno = ?",
                (appointment_no,),
            )
            transaction_data["NotificationActionsQueue"] = notification_queue_df.to_dict("records")
            
            # Get Notification_Item records for each ItemId from the queue
            notification_items = []
            if not notification_queue_df.empty:
                item_ids = notification_queue_df["ID"].dropna().unique().tolist()
                for item_id in item_ids:
                    try:
                        notification_item_df = fetch_df(
                            cur,
                            "SELECT * FROM Notification_Item WHERE ItemId = ?",
                            (item_id,),
                        )
                        if not notification_item_df.empty:
                            notification_items.extend(notification_item_df.to_dict("records"))
                    except pyodbc.Error as e:
                        print(f"⚠️  Notification_Item not accessible for ItemId {item_id}: {e}")
            
            transaction_data["NotificationItem"] = notification_items
        except pyodbc.Error as e:
            print(f"⚠️  Notification_Actions_Queue table not accessible: {e}")
            transaction_data["NotificationActionsQueue"] = []
            transaction_data["NotificationItem"] = []

        # Message_Transaction_Details
        try:
            message_trans_df = fetch_df(
                cur,
                "SELECT * FROM Message_Transaction_Details WHERE appno = ?",
                (appointment_no,),
            )
            transaction_data["MessageTransactionDetails"] = message_trans_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  Message_Transaction_Details table not accessible: {e}")
            transaction_data["MessageTransactionDetails"] = []

        # PaymentTransactionDetails
        try:
            payment_trans_df = fetch_df(
                cur,
                "SELECT * FROM PaymentTransactionDetails WHERE appno = ?",
                (appointment_no,),
            )
            transaction_data["PaymentTransactionDetails"] = payment_trans_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  PaymentTransactionDetails table not accessible: {e}")
            transaction_data["PaymentTransactionDetails"] = []

        # AppointmentPayments
        try:
            appt_payments_df = fetch_df(
                cur,
                "SELECT * FROM AppointmentPayments WHERE AppointmentId = ?",
                (appt_id,),
            )
            transaction_data["AppointmentPayments"] = appt_payments_df.to_dict("records")
        except pyodbc.Error as e:
            print(f"⚠️  AppointmentPayments table not accessible: {e}")
            transaction_data["AppointmentPayments"] = []

        # # 6. Get Answers (direct from appointment) - COMMENTED OUT
        # try:
        #     answers_df = fetch_df(
        #         cur,
        #         "SELECT * FROM AnswersTemp WHERE AppNo = ? AND Answer IS NOT NULL ORDER BY 1 DESC",
        #         (appointment_no,),
        #     )
        #     answers_data = answers_df.to_dict("records")
        # except pyodbc.Error as e:
        #     print(f"⚠️  AnswersTemp table not accessible: {e}")
        #     answers_data = []
        
        # Set answers_data to empty list since it's commented out
        answers_data = []

        # Build the hierarchical JSON structure matching the flowchart
        return {
            "ExtractedAt": datetime.now(timezone.utc).isoformat(timespec="seconds").replace("+00:00", "Z"),
            "QueryParameters": {
                "MRN": mrn,
                "AppointmentNo": appointment_no
            },
            "User": {
                **user,
                "Appointments": [{
                    "AppointmentDetails": appointment,
                    "AppointmentSupport": support_hierarchy,
                    "TransactionData": {
                        "TransactionNo": appointment_no,
                        **transaction_data
                    },
                    "Answers": answers_data,
                    "Summary": {
                        "TotalSupportRecords": len(support_hierarchy),
                        "TotalWorkflowAudits": sum(len(s.get("WorkflowAuditLog", [])) for s in support_hierarchy),
                        "TotalKioskWorkflows": sum(
                            sum(1 for w in s.get("WorkflowAuditLog", []) if w.get("KioskWorkflow"))
                            for s in support_hierarchy
                        ),
                        "TotalQuestionTemplates": sum(
                            sum(1 for w in s.get("WorkflowAuditLog", []) 
                                if w.get("KioskWorkflow") and w["KioskWorkflow"].get("QuestionTemplate"))
                            for s in support_hierarchy
                        ),
                        "TotalOutboundDocuments": len(transaction_data.get("OutboundDocuments", [])),
                        "TotalDocumentManagementDetails": len(transaction_data.get("DocumentManagementDetails", [])),
                        "TotalAPITransactions": len(transaction_data.get("APITransactions", [])),
                        "TotalAnswers": len(answers_data),
                    }
                }]
            }
        }


# ────────────── 2.  CLI ENTRY ──────────────
if __name__ == "__main__":
    if len(sys.argv) != 3:
        sys.exit("Usage: python trace_mrn_appointment.py <MRN> <AppointmentNo>")

    try:
        payload = build_mrn_appointment_json(sys.argv[1], sys.argv[2])
    except Exception as exc:
        sys.exit(f"❌  {exc}")

    # Print summary with colors
    class C:
        HEADER = '\033[95m'
        OKBLUE = '\033[94m'
        OKCYAN = '\033[96m'
        OKGREEN = '\033[92m'
        WARNING = '\033[93m'
        FAIL = '\033[91m'
        ENDC = '\033[0m'
        BOLD = '\033[1m'

    user = payload["User"]
    appointment_data = user["Appointments"][0]
    appt = appointment_data["AppointmentDetails"]
    summary = appointment_data["Summary"]

    print(f"\n{C.BOLD}{C.HEADER}=== APPOINTMENT DATA EXTRACTION ==={C.ENDC}")
    print(f"{C.OKGREEN}User:{C.ENDC} {user.get('FirstName', '')} {user.get('LastName', '')} (MRN: {user.get('MRN', '')})")
    print(f"{C.OKGREEN}Appointment:{C.ENDC} {appt.get('AppNo', '')} (ID: {appt.get('AppointmentId', appt.get('AppId', ''))})")
    
    print(f"\n{C.BOLD}{C.OKCYAN}=== DATA SUMMARY ==={C.ENDC}")
    print(f"├─ {C.OKBLUE}Support Records:{C.ENDC} {summary['TotalSupportRecords']}")
    print(f"├─ {C.WARNING}Workflow Audits:{C.ENDC} {summary['TotalWorkflowAudits']}")
    print(f"├─ {C.FAIL}Kiosk Workflows:{C.ENDC} {summary['TotalKioskWorkflows']}")
    print(f"├─ {C.HEADER}Question Templates:{C.ENDC} {summary['TotalQuestionTemplates']}")
    print(f"├─ {C.OKCYAN}Outbound Documents:{C.ENDC} {summary['TotalOutboundDocuments']}")
    print(f"├─ {C.OKGREEN}Document Management:{C.ENDC} {summary['TotalDocumentManagementDetails']}")
    print(f"├─ {C.OKBLUE}API Transactions:{C.ENDC} {summary['TotalAPITransactions']}")
    print(f"└─ {C.WARNING}Answers:{C.ENDC} {summary['TotalAnswers']}")

    # Write to JSON file
    outfile = f"MRN_{sys.argv[1]}_Appt_{sys.argv[2]}.json".replace(" ", "_")
    with open(outfile, "w", encoding="utf-8") as f:
        json.dump(payload, f, indent=2, default=str)
    
    print(f"\n✅  Wrote full JSON to {C.BOLD}{outfile}{C.ENDC}")
    
    # Print workflow hierarchy details
    if summary['TotalKioskWorkflows'] > 0:
        print(f"\n{C.BOLD}{C.HEADER}=== WORKFLOW HIERARCHY ==={C.ENDC}")
        for i, support in enumerate(appointment_data["AppointmentSupport"]):
            if support.get("WorkflowAuditLog"):
                print(f"\n{C.OKGREEN}Support Record {i+1} (ID: {support.get('AppointmentId')}):{C.ENDC}")
                for j, audit in enumerate(support["WorkflowAuditLog"]):
                    if audit.get("KioskWorkflow"):
                        kiosk = audit["KioskWorkflow"]
                        print(f"  ├─ Workflow Audit {j+1}")
                        print(f"  │  └─ KioskWorkflow: {kiosk.get('KioskWorkflowId')}")
                        if kiosk.get("QuestionTemplate"):
                            print(f"  │     └─ QuestionTemplate: {kiosk['QuestionTemplate'].get('QuestionTempId')}") 
