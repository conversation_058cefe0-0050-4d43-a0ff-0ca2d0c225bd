{"ExtractedAt": "2025-07-15T11:35:58Z", "QueryParameters": {"MRN": "7811", "AppointmentNo": "1095391"}, "User": {"UserID": 55, "OrganizationId": 102, "UserTypeId": 1, "PalmReaderValue": null, "LastName": "<PERSON>", "FirstName": "<PERSON>", "MiddleName": null, "Initial": null, "Sex": "Male", "UserName": null, "Password": null, "OtherId": "7811", "OtherId2": null, "EmailId": "<EMAIL>", "HasImage": true, "SSN": "352452369", "DOB": "1995-05-05 00:00:00", "MRN": "7811", "Address1": "25/2, Laned", "Address2": "<PERSON>", "City": "AURORA", "State": "DE", "Zip": "50012", "Active": true, "PasswordExpiryDate": null, "Depts": null, "UserStageID": null, "FamilyNo": null, "PersonNo": null, "FPTemplateI": null, "FPTemplateII": null, "FPTemplateIII": null, "LocationID": 11, "CloudUserID": null, "PayerID": null, "EligibilityResp": null, "DpFPTemplate": null, "ViewReports": false, "Country": "USA", "UserImageDateTaken": "2024-05-30 03:05:54.050000", "PatientTypeID": null, "PasswordChanged": false, "HasExmStatusUpdPermission": false, "SignImage": null, "Passcode": null, "Salt": null, "PasswordFailure": null, "LastSuccessfulLogin": null, "LastFailureLogin": null, "IsLocked": null, "IsGoogleAuthAccountLinked": null, "Source": "SP-SIU", "IsToBeMajorAlert": "N", "PasswordResetAttempt": 0, "LastPasswordResetRequestUTC": "1900-01-01 00:00:00", "Message": null, "CreatedDTTM": "2024-05-23 03:33:31.917000", "ModifiedDTTM": "2024-05-30 03:48:05.333000", "Appointments": [{"AppointmentDetails": {"AppointmentId": 94, "OrganizationId": 102, "LocationId": 11, "Department": "Dummy", "UserID": 55, "AppNo": "1095391", "AppDate": "2024-05-23 15:00:00", "AppTime": "2024-05-23 15:00:00", "AppDuration": null, "AppCode": "Gau", "AppDesc": null, "ArrTime": "2024-05-23 13:04:35.963000", "AppEndDateTime": null, "AppEndTime": null, "ProviderId": 106, "ProviderResourceID": "", "ProcedureCode": "1188", "ProcedureDesc": null, "DepartmentCode": null, "Reasonforvisit": "NEW PATIENT 45", "Status": "Check-in completed", "Remarks": ";LanguageID:1;:29;QuestionTemplateID:58", "IsManual": null, "IsManualCheck": true, "CheckinTypeID": 3, "LastModified": "2024-05-23 03:34:15.213000", "AppointmentStageID": null, "ExtDepartmentCode": "1111", "Copay": null, "GenCopay": null, "WalkinAppNo": null, "ServiceIds": null, "ExpectedTime": null, "IsPaymentDone": false, "CheckedInByName": null, "CheckedInById": 2, "ReachedFinalSummary": true, "AppointmentPriorityID": null, "CheckinFrom": 0, "MobilityID": null, "KioskSessionEndTime": "2024-05-23 03:53:07.430000", "MRGOtherID": "7811", "Progress": "3%Current Address*3%Email*3%Cell Phone*3%Work Phone*3%pic*3%Subscriber*3%InsCardQst*3%secondary Subscriber Details*3%SecondaryCarrierCode*3%SecondaryCarrierName*3%SecondaryClaimMemberID*3%CarrierCode*3%PayerID*3%PayerLookupId*3%PlanCode*3%SubscriberID*3%PlanName*3%secondaryinsu*3%driving*3%Blank doc*3%painsheet*3%AssignOfBenefits*3%clinical_dynamic*3%PrivacyNotice*3%FINANCIAL RESPONSIBILITY AGREEMENT*3%tele*3%CoPay*3%PaymentAgreement*^1095391", "IsScheduled": true, "EncounterId": null, "LoggedInUserId": 2, "CompleteTime": "2024-05-23 13:23:15.877000", "ApptCompletedClientDTTM": "2024-05-23 14:23:16.193000", "ExamInstructions": null, "QuestionnaireProgress": null, "EligibilityStatus": "", "IsPayByDesk": true, "NotificationType": "Action", "OnDemandWorkflowStatus": "O", "IsApptStickyNote": false, "ActualAppDate": "2024-05-23", "ApptEligibilitySummaryId": null, "CreatedServerDTTM": "2024-05-23 03:34:15.877000", "AppointmentStartDTTM": null}, "AppointmentSupport": [{"ID": 94, "OrganizationID": 102, "AppointmentId": 94, "AppointmentNo": "1095391", "RescheduledAppointmentNo": "0", "AppointmentStatusOutboundStatus": null, "QuestionTemplateID": 58, "LastAnsweredScreenNumber": 19, "PreRegistrationStatus": "", "PaymentOutboundStatus": null, "DocumentOutboundStatus": null, "Source": "SIU", "DeviceCheckinStatusID": 0, "GroupCheckinStatusID": null, "PMSLastUpdatedDTTM": null, "WorkflowScreenLastAccessUTCDTTM": "2024-05-23 07:52:55.330000", "CheckedInFrom": "RadAsyst", "PatientConfirmWorkflow": "Dashboard", "PaymentCompletedWorkflow": null, "Message": "", "TeleHealthWaiting": "N", "IsAppointmentConfirmed": "N", "ApptConfirmationUTCDTTM": null, "PreManualCheckinTypeID": 2, "WorkflowCompleted": true, "IsBalanceUpdateFail": null, "IsPatientAtClinic": "N", "IsHIPAANoteSent": "N", "IsReleaseNoteSent": "N", "IsNoticeOfPrivacyNoteSent": "N", "IsAssignBenefitNoteSent": "N", "CheckinModEntryCode": "DashBoard", "EncounterId": "47449", "ScreeningPercentage": 112, "CreatedBy": 0, "CreatedDTTM": "2024-05-23 03:34:15.920000", "ModifiedDTTM": "2024-05-23 03:52:55.330000", "DocumentOutboundFailedIDs": null, "LanguageID": 1, "BillableProviderId": null, "ApptCancelReason": null, "ManualCheckinLockTime": "2024-05-23 07:53:30.877000", "CheckinType": "checkin", "IsTeleHealthNotificationSent": "N", "WorkflowSource": "", "WorkflowStartDTTM": "2024-05-23 07:34:35.990000", "DocumentOutboundFailedIDList": null, "ReachedPayment": "Y", "ReconcileStatus": "N", "InterfaceReconcileAckId": null, "IsEventARRFlag": null, "EventARRUpdatedDTTM": null, "IsEventCheckinFlag": null, "EventCheckinUpdatedDTTM": null, "PreRegServerDTTM": null, "CancelledServerDTTM": null, "WorkflowAuditLog": [{"WorkflowAuditLogId": "43", "AppointmentId": 94, "OrganizationId": 102, "LanguageId": 1, "PatientID": 55, "TypeOfCheckin": "", "UserId": 2, "Status": "Completed", "WorkflowType": "ReasonforvisitWorkflow|Gau", "LastAnsweredScreenNo": "9", "ScreensAccessed": "Signature Pad, Payment, Final Summary: MsgBalancePending_RA, Thank You. Payment is not completely done. Please meet with the receptionist.  If you are using one of our iPads, please return it to the front desk now.  If you are using your own device, we will call your name shortly. Thank you for choosing Cartersville Office-!.", "Channel": null, "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "IPAddress": "************", "DeviceID": "", "SessionID": "jbgozovemdwdtlsqzdo4g2yl", "StartDate": "2024-05-23 13:04:35.573000", "EndDate": "2024-05-23 13:23:06.627000", "IsWorkList": false, "IsWorkFlowCompleted": true, "KioskWorkflowId": 133, "ClientPracticeID": "", "IsMaxID": "N", "CreatedBy": 2, "CreatedDate": "2024-05-23 03:34:35.583000", "ModifiedBy": 2.0, "ModifiedDate": "2024-05-23 03:53:06.657000", "WorkflowSource": "", "ExitScreenNumber": null, "ExitScreenName": null, "DateTimeofExit": null, "IsDocumentSign": null, "KioskWorkflow": {"KioskWorkflowId": 133, "WorkflowType": "ReasonforvisitWorkflow", "Expression": "Gau", "Organizationid": 102, "DepartmentID": null, "LocationId": null, "QuestionTemplateId": 58, "IsActive": true, "IsWorkList": false, "AppTypeId": 2, "UserTypeId": 0, "Source": "ACMT", "CreatedDTTM": "2024-05-23 01:20:04.750000", "ModifiedDTTM": null, "QuestionTemplate": {"QuestionTempId": 58, "OrganizationId": 102, "LanguageId": 1, "Group": "Active", "TemplateName": "New patient SP1", "HeaderText": "New patient/emergency  appointments", "FooterText": "", "Instruction": "", "DeptId": null, "TemplateQuery": "Select column844 as [1_guarantor]column851 as [1_Nameuarantor]column858 as [1_patient_guarantor]column845 as [2_guarantor]column852 as [2_Nameuarantor]column859 as [2_patient_guarantor]column846 as [3_guarantor]column853 as [3_Nameuarantor]column860 as [3_patient_guarantor]column847 as [4_guarantor]column854 as [4_Nameuarantor]column861 as [4_patient_guarantor]column848 as [5_guarantor]column855 as [5_Nameuarantor]column862 as [5_patient_guarantor]column849 as [6_guarantor]column856 as [6_Nameuarantor]column863 as [6_patient_guarantor]column850 as [7_guarantor]column857 as [7_Nameuarantor]column864 as [7_patient_guarantor]Column35 as [AccessNo]Column10 as [Address1]Column11 as [Address2]Column26 as [Age]column838 as [Aging<PERSON>JCF<PERSON>]column832 as [<PERSON><PERSON><PERSON><PERSON>]column837 as [A<PERSON><PERSON><PERSON>]column836 as [<PERSON><PERSON><PERSON>]column831 as [<PERSON><PERSON><PERSON><PERSON>]column830 as [AgingNPMedHist]column834 as [<PERSON><PERSON>OTNP]column835 as [AgingOTSplintFR]column841 as [AgingPI]column833 as [AgingPTNP]column839 as [AgingRJPSpine]column840 as [AgingSSCSpine]column812 as [AllergicToSeafood]column813 as [AllergicToshellfish]column865 as [AreYouGuarantor]column814 as [AuthPersonOne]column817 as [AuthPersonOneDOB]column816 as [AuthPersonThree]column819 as [AuthPersonThreeDOB]column815 as [AuthPersonTwo]column818 as [AuthPersonTwoDOB]column820 as [BodyPartAnotherQ]column821 as [BodyPartAnotherQ1]column822 as [BodyPartAnotherQ2]column823 as [BodyPartAnotherQ3]column356 as [BoneDensityDate]column591 as [BoneDensityDateNew]column901 as [CardSwipe]Column808 as [CarrierCode]column882 as [CarrierID]column883 as [CarrierName]column902 as [Cash]Column12 as [City]column890 as [ClaimMemberID]Column39 as [Comments]Column36 as [ContrastconsentForm]Column23 as [CoPay]column797 as [CoPayMBO]column798 as [CoPayMBOHDR]column892 as [CoverageOrder]Column57 as [Date]Column24 as [DOB]Column60 as [DrugAllergies]Column54 as [Dx_Codes]Column15 as [Email]column81 as [EmployerCode]column79 as [EmployerContact]column80 as [EmployerName]column64 as [Ethnicity]Column37 as [Examcode]Column32 as [ExamDesc]Column33 as [Examtype]column897 as [FamAsthma]column898 as [Famcancer]column829 as [FinancialRespDoc]column875 as [FinGuarantorAdd]column874 as [FinGuarantorDOB]column873 as [FinGuarantorName]column842 as [FinRespDefault]column799 as [FinSummaryMBO]column800 as [FinSummaryMBOHDR]Column6 as [Firstname]column825 as [FJCDoc2]Column25 as [Gender]column893 as [GroupNumber]column843 as [Header_ELECTRONICALL]Column29 as [Height]Column30 as [Height_Units]Column42 as [HIPAA]column592 as [HowDidYouHearAboutUs]column891 as [IN2PolicyHolderDOB]column886 as [IN2Relationship]column888 as [IN3PolicyHolderDOB]column889 as [IN3Relationship]Column19 as [InsuranceImage]column828 as [InsuranceImageBack]column876 as [IsFinGuarantor]column879 as [IsFinGuarantorAddSam]column867 as [IsGuarantor]column878 as [IsParentAddressSame]column885 as [IsPolicyInCoverageList]Column7 as [Lastname]column900 as [ManualCard]column66 as [MaritalStatus]column358 as [MBOPatientInfoDoc]column357 as [MBOPatientInfoHdr]column360 as [MBOSecIns]column362 as [MBOSecInsGroupId]column359 as [MBOSecInsHdr]column364 as [MBOSecInsHolDOB]column363 as [MBOSecInsHolName]column366 as [MBOSecInsHolRel]column365 as [MBOSecInsHolSSN]column361 as [MBOSecInsMemberId]column810 as [MedHistALGY]column811 as [MedHistALGYNDRUG]Column8 as [Middlename]Column116 as [Mobile_Phone]Column34 as [MRN]column866 as [NameGuarantor]column809 as [NewPHeatCold]column71 as [NextKinAddress1]column70 as [NextKinAddress2]column72 as [NextKinBusinessPhone]column69 as [NextKinCity]column75 as [NextKinFirstName]column74 as [NextKinLastName]column77 as [NextKinMiddleName]column73 as [NextKinMobilePhone]column76 as [NextKinPhone]column78 as [NextKinRelationship]column68 as [NextKinState]column67 as [NextKinZip]Column31 as [OrderReason]column880 as [othermedcon_Qst]column881 as [othermedcon_Val]Column58 as [PaientSignatureDate]column801 as [PainAssessHdr]column802 as [PainAssessmentDoc]column803 as [PainAssessmentDocHdr]column804 as [PainDescribeMbo]column805 as [PainScaleMbo]column806 as [PainSheetDocMBO]column807 as [PAINSHEETHDR]column877 as [ParkingQ]Column21 as [Patient_Consent]Column40 as [PatientAccountNumber]Column286 as [PatientBalancePaid]Column20 as [PatientIDImage]Column272 as [PatientPrePay]column895 as [PatientRelationToSubscriber]column894 as [PatientRelationToSubscriberCode]Column41 as [PatientRemarks]column903 as [PayAtDesk]column826 as [PayerID]column827 as [PayerLookupId]column82 as [PCPProvider]column441 as [PharmacyDetails]column896 as [PlanCode]column884 as [PlanID]column887 as [PlanName]Column17 as [Primary_Phone]Column46 as [PrimaryCompID]Column48 as [PrimaryCompName]Column50 as [PrimaryEffDate]Column52 as [PrimaryExpDate]column824 as [PrimaryLanguage]Column44 as [PrimaryPolicyNo]Column84 as [ProviderSpecialty]column708 as [PTNewPatientPacke]Column107 as [Question107]Column108 as [Question108]Column109 as [Question109]Column110 as [Question110]Column111 as [Question111]Column112 as [Question112]Column113 as [Question113]Column114 as [Question114]Column115 as [Question115]Column117 as [Question117]Column118 as [Question118]Column119 as [Question119]Column120 as [Question120]Column121 as [Question121]Column122 as [Question122]Column123 as [Question123]Column124 as [Question124]Column125 as [Question125]Column126 as [Question126]Column127 as [Question127]Column128 as [Question128]Column129 as [Question129]Column130 as [Question130]Column131 as [Question131]Column132 as [Question132]Column133 as [Question133]Column134 as [Question134]Column135 as [Question135]Column136 as [Question136]Column137 as [Question137]Column138 as [Question138]Column139 as [Question139]Column140 as [Question140]Column141 as [Question141]Column142 as [Question142]Column143 as [Question145]Column144 as [Question146]Column145 as [Question147]Column146 as [Question148]Column147 as [Question149]Column148 as [Question150]Column149 as [Question151]Column150 as [Question152]Column151 as [Question153]Column152 as [Question154]Column153 as [Question155]Column154 as [Question156]Column155 as [Question157]Column156 as [Question158]Column157 as [Question159]Column158 as [Question160]Column159 as [Question161]Column160 as [Question162]Column161 as [Question163]Column162 as [Question164]Column163 as [Question165]Column164 as [Question166]Column165 as [Question167]Column166 as [Question168]Column167 as [Question169]Column168 as [Question170]Column169 as [Question171]Column170 as [Question172]Column171 as [Question173]Column172 as [Question174]Column173 as [Question175]Column174 as [Question176]Column175 as [Question177]Column176 as [Question178]Column177 as [Question179]Column178 as [Question180]Column179 as [Question181]Column180 as [Question182]Column181 as [Question183]Column182 as [Question184]Column183 as [Question185]Column184 as [Question186]Column185 as [Question187]Column186 as [Question188]Column187 as [Question189]Column188 as [Question190]Column189 as [Question191]Column190 as [Question192]Column191 as [Question193]Column192 as [Question194]Column193 as [Question195]Column194 as [Question196]Column195 as [Question197]Column196 as [Question198]Column197 as [Question199]Column198 as [Question200]Column199 as [Question201]Column200 as [Question202]Column201 as [Question203]Column202 as [Question204]Column203 as [Question205]Column204 as [Question206]Column205 as [Question207]Column206 as [Question208]Column207 as [Question209]Column208 as [Question210]Column209 as [Question211]Column210 as [Question212]Column211 as [Question213]Column212 as [Question214]Column213 as [Question215]Column214 as [Question216]Column215 as [Question217]Column216 as [Question218]Column217 as [Question219]Column218 as [Question220]Column219 as [Question221]Column220 as [Question222]Column221 as [Question223]Column222 as [Question224]Column223 as [Question225]Column224 as [Question226]Column225 as [Question227]Column226 as [Question228]Column227 as [Question229]Column228 as [Question230]Column229 as [Question231]Column230 as [Question232]Column231 as [Question233]Column232 as [Question234]Column233 as [Question235]Column234 as [Question236]Column235 as [Question237]Column236 as [Question238]Column237 as [Question239]Column238 as [Question240]Column239 as [Question241]Column240 as [Question242]Column241 as [Question243]Column242 as [Question244]Column243 as [Question245]Column244 as [Question246]Column245 as [Question247]Column246 as [Question248]Column247 as [Question249]Column248 as [Question250]Column249 as [Question251]Column250 as [Question252]Column251 as [Question253]Column252 as [Question254]Column253 as [Question255]Column254 as [Question256]Column255 as [Question257]Column256 as [Question258]Column257 as [Question259]Column258 as [Question260]Column259 as [Question261]Column260 as [Question262]Column261 as [Question263]Column262 as [Question264]Column263 as [Question265]Column264 as [Question266]Column265 as [Question267]Column266 as [Question268]Column267 as [Question269]Column268 as [Question270]Column269 as [Question271]Column270 as [Question272]Column271 as [Question273]Column272 as [Question274]Column273 as [Question275]Column274 as [Question276]Column275 as [Question277]Column276 as [Question278]Column277 as [Question279]Column278 as [Question280]Column279 as [Question281]Column280 as [Question282]Column281 as [Question283]Column282 as [Question284]Column283 as [Question285]Column284 as [Question286]Column285 as [Question287]Column286 as [Question288]Column287 as [Question289]Column288 as [Question290]Column289 as [Question291]Column290 as [Question292]Column291 as [Question293]Column292 as [Question294]Column293 as [Question295]Column294 as [Question296]Column295 as [Question297]Column296 as [Question298]Column297 as [Question299]Column298 as [Question300]Column299 as [Question301]Column300 as [Question302]column301 as [Question303]column302 as [Question304]column303 as [Question305]column304 as [Question306]column305 as [Question307]column306 as [Question308]column307 as [Question309]column308 as [Question310]column309 as [Question311]column310 as [Question312]column311 as [Question313]column312 as [Question314]column313 as [Question315]column314 as [Question316]column315 as [Question317]column316 as [Question318]column317 as [Question319]column318 as [Question320]column319 as [Question321]column320 as [Question322]column321 as [Question323]column322 as [Question324]column323 as [Question325]column324 as [Question326]column325 as [Question327]column326 as [Question328]column327 as [Question329]column328 as [Question330]column329 as [Question331]column330 as [Question332]column331 as [Question333]column332 as [Question334]column333 as [Question335]column334 as [Question336]column335 as [Question337]column336 as [Question338]column337 as [Question339]column338 as [Question340]column339 as [Question341]column340 as [Question342]column341 as [Question343]column342 as [Question344]column343 as [Question345]column344 as [Question346]column345 as [Question347]column346 as [Question348]column347 as [Question349]column348 as [Question350]column349 as [Question351]column350 as [Question352]column351 as [Question353]column352 as [Question354]column353 as [Question355]column354 as [Question356]column355 as [Question357]column367 as [Question358]column368 as [Question359]column369 as [Question360]column370 as [Question361]column371 as [Question362]column372 as [Question365]column373 as [Question366]column374 as [Question367]column375 as [Question368]column376 as [Question369]column377 as [Question370]column378 as [Question371]column379 as [Question372]column380 as [Question373]column381 as [Question374]column382 as [Question375]column383 as [Question376]column384 as [Question377]column385 as [Question378]column386 as [Question379]column387 as [Question380]column388 as [Question381]column389 as [Question382]column390 as [Question383]column391 as [Question384]column392 as [Question385]column393 as [Question386]column394 as [Question387]column395 as [Question388]column396 as [Question389]column397 as [Question390]column398 as [Question391]column399 as [Question392]column400 as [Question393]column408 as [Question394]column409 as [Question395]column410 as [Question396]column411 as [Question397]column412 as [Question398]column413 as [Question399]column414 as [Question400]column415 as [Question408]column416 as [Question409]column417 as [Question410]column418 as [Question411]column419 as [Question412]column420 as [Question413]column421 as [Question414]column422 as [Question415]column423 as [Question416]column424 as [Question417]column425 as [Question418]column426 as [Question419]column427 as [Question420]column428 as [Question421]column429 as [Question422]column430 as [Question423]column431 as [Question424]column432 as [Question425]column433 as [Question426]column434 as [Question427]column435 as [Question428]column436 as [Question429]column437 as [Question430]column438 as [Question431]column439 as [Question432]column440 as [Question433]column904 as [Question434]column442 as [Question435]column443 as [Question436]column444 as [Question437]column445 as [Question438]column446 as [Question439]column447 as [Question440]column448 as [Question441]column449 as [Question442]column450 as [Question443]column451 as [Question444]column452 as [Question445]column453 as [Question446]column454 as [Question447]column455 as [Question448]column456 as [Question449]column457 as [Question450]column458 as [Question451]column459 as [Question452]column460 as [Question453]column461 as [Question454]column462 as [Question455]column463 as [Question456]column464 as [Question457]column465 as [Question458]column466 as [Question459]column467 as [Question460]column468 as [Question461]column469 as [Question462]column470 as [Question463]column471 as [Question464]column472 as [Question465]column473 as [Question466]column474 as [Question467]column475 as [Question468]column476 as [Question469]column477 as [Question470]column478 as [Question471]column479 as [Question472]column480 as [Question473]column481 as [Question474]column482 as [Question475]column483 as [Question476]column484 as [Question477]column485 as [Question478]column486 as [Question479]column487 as [Question480]column488 as [Question481]column489 as [Question482]column490 as [Question483]column491 as [Question484]column492 as [Question485]column493 as [Question486]column494 as [Question487]column495 as [Question488]column496 as [Question489]column497 as [Question490]column498 as [Question491]column499 as [Question492]column500 as [Question493]column501 as [Question494]column502 as [Question495]column503 as [Question496]column504 as [Question497]column505 as [Question498]column506 as [Question499]column507 as [Question500]column508 as [Question501]column509 as [Question502]column510 as [Question503]column511 as [Question504]column512 as [Question505]column513 as [Question506]column514 as [Question507]column515 as [Question508]column516 as [Question509]column517 as [Question510]column518 as [Question511]column519 as [Question512]column520 as [Question513]column521 as [Question514]column522 as [Question515]column523 as [Question516]column524 as [Question517]column525 as [Question518]column526 as [Question519]column527 as [Question520]column528 as [Question521]column529 as [Question522]column530 as [Question523]column531 as [Question524]column532 as [Question525]column533 as [Question526]column534 as [Question527]column535 as [Question528]column536 as [Question529]column537 as [Question530]column538 as [Question531]column539 as [Question532]column540 as [Question533]column541 as [Question534]column542 as [Question535]column543 as [Question536]column544 as [Question537]column545 as [Question538]column546 as [Question539]column547 as [Question540]column548 as [Question541]column549 as [Question542]column550 as [Question543]column551 as [Question544]column552 as [Question545]column553 as [Question546]column554 as [Question547]column555 as [Question548]column556 as [Question549]column557 as [Question550]column558 as [Question551]column559 as [Question552]column560 as [Question553]column561 as [Question554]column562 as [Question555]column563 as [Question556]column564 as [Question557]column565 as [Question558]column566 as [Question559]column567 as [Question560]column568 as [Question561]column569 as [Question562]column570 as [Question563]column571 as [Question564]column572 as [Question565]column573 as [Question566]column574 as [Question567]column575 as [Question568]column576 as [Question569]column577 as [Question570]column578 as [Question571]column579 as [Question572]column580 as [Question573]column581 as [Question574]column582 as [Question575]column584 as [Question577]column586 as [Question579]column587 as [Question581]column588 as [Question582]column589 as [Question583]column590 as [Question584]column593 as [Question585]column594 as [Question586]column595 as [Question587]column596 as [Question588]column597 as [Question589]column598 as [Question590]column599 as [Question591]column600 as [Question592]column601 as [Question593]column602 as [Question594]column603 as [Question595]column604 as [Question596]column605 as [Question597]column606 as [Question598]column607 as [Question599]column608 as [Question600]column609 as [Question601]column610 as [Question602]column611 as [Question603]column612 as [Question604]column613 as [Question605]column614 as [Question606]column615 as [Question607]column616 as [Question608]column617 as [Question609]column618 as [Question610]column619 as [Question611]column620 as [Question612]column621 as [Question613]column622 as [Question614]column623 as [Question615]column624 as [Question616]column625 as [Question617]column626 as [Question618]column627 as [Question619]column628 as [Question620]column629 as [Question621]column630 as [Question622]column631 as [Question623]column632 as [Question624]column633 as [Question625]column634 as [Question626]column635 as [Question627]column636 as [Question628]column637 as [Question629]column638 as [Question630]column639 as [Question631]column640 as [Question632]column641 as [Question633]column642 as [Question634]column643 as [Question635]column644 as [Question636]column645 as [Question637]column646 as [Question638]column647 as [Question639]column648 as [Question640]column649 as [Question641]column650 as [Question642]column651 as [Question643]column652 as [Question644]column653 as [Question645]column654 as [Question646]column655 as [Question647]column656 as [Question648]column657 as [Question649]column658 as [Question650]column659 as [Question651]column660 as [Question652]column661 as [Question653]column662 as [Question654]column663 as [Question655]column664 as [Question656]column665 as [Question657]column666 as [Question658]column667 as [Question659]column668 as [Question660]column669 as [Question661]column670 as [Question662]column671 as [Question663]column672 as [Question664]column673 as [Question665]column674 as [Question666]column675 as [Question667]column676 as [Question669]column677 as [Question671]column678 as [Question673]column679 as [Question674]column680 as [Question675]column681 as [Question676]column791 as [Question677]column792 as [Question678]column793 as [Question679]column794 as [Question680]column795 as [Question681]column796 as [Question682]column684 as [Question683]column685 as [Question684]column686 as [Question685]column687 as [Question686]column688 as [Question687]column689 as [Question688]column690 as [Question689]column691 as [Question690]column692 as [Question691]column693 as [Question692]column694 as [Question693]column695 as [Question694]column696 as [Question695]column697 as [Question696]column698 as [Question697]column699 as [Question698]column700 as [Question699]column701 as [Question700]column702 as [Question701]column703 as [Question702]column704 as [Question703]column705 as [Question704]column706 as [Question705]column707 as [Question706]column709 as [Question707]column710 as [Question708]column711 as [Question709]column712 as [Question710]column713 as [Question711]column714 as [Question712]column715 as [Question713]column716 as [Question714]column717 as [Question715]column718 as [Question716]column719 as [Question717]column720 as [Question718]column721 as [Question719]column722 as [Question720]column723 as [Question721]column724 as [Question722]column725 as [Question723]column726 as [Question724]column727 as [Question725]column728 as [Question726]column729 as [Question727]column730 as [Question728]column731 as [Question729]column732 as [Question730]column733 as [Question731]column734 as [Question732]column735 as [Question733]column736 as [Question734]column737 as [Question735]column738 as [Question736]column739 as [Question737]column740 as [Question738]column741 as [Question739]column742 as [Question740]column743 as [Question741]column744 as [Question742]column745 as [Question743]column746 as [Question744]column747 as [Question745]column748 as [Question746]column749 as [Question747]column750 as [Question748]column751 as [Question749]column752 as [Question750]column753 as [Question751]column754 as [Question752]column755 as [Question753]column756 as [Question754]column757 as [Question755]column758 as [Question756]column759 as [Question757]column760 as [Question758]column761 as [Question759]column762 as [Question760]column763 as [Question761]column764 as [Question762]column765 as [Question763]column766 as [Question764]column767 as [Question765]column768 as [Question766]column769 as [Question767]column770 as [Question768]column771 as [Question769]column772 as [Question770]column773 as [Question771]column774 as [Question772]column775 as [Question773]column776 as [Question774]column777 as [Question775]column778 as [Question776]column779 as [Question777]column780 as [Question778]column781 as [Question779]column782 as [Question780]column783 as [Question781]column784 as [Question782]column785 as [Question784]column786 as [Question785]column787 as [Question786]column788 as [Question787]column789 as [Question788]column790 as [Question789]column905 as [Question905]column906 as [Question906]column907 as [Question907]column908 as [Question908]column909 as [Question909]column910 as [Question910]column911 as [Question911]column912 as [Question912]column913 as [Question913]column914 as [Question914]column915 as [Question915]column916 as [Question916]Column61 as [QuestionSummary]column65 as [Race]Column38 as [Reason]Column16 as [Reasonforvisit]column83 as [ReferProvider]column899 as [SavedCard]Column18 as [Secondary_Phone]Column85 as [SecondaryCarrierCode]Column86 as [SecondaryCarrierID]Column87 as [SecondaryCarrierName]Column88 as [SecondaryClaimMemberID]Column47 as [SecondaryCompID]Column49 as [SecondaryCompName]Column51 as [SecondaryEffDate]Column53 as [SecondaryExpDate]Column45 as [SecondaryPolicyNo]Column89 as [SecSubscriberFirstName]Column90 as [SecSubscriberGenderCode]Column91 as [SecSubscriberLastName]Column22 as [Signature]column682 as [SPINEFJCTWO]column585 as [SPINEFORMSSCONE]column683 as [SPINEFORMSSCTWO]column583 as [SPNIEFJCONE]column872 as [SSCformAnyothertest]column868 as [SSCformbackprob]column871 as [SSCformInjection]column870 as [SSCFormPTherapy]column869 as [SSCformspinesurg]Column9 as [SSN]Column13 as [State]Column92 as [SubscriberDOB]Column93 as [SubscriberFirstName]Column94 as [SubscriberGenderCode]Column95 as [SubscriberGenderName]Column96 as [SubscriberID]Column97 as [SubscriberLastName]Column98 as [SubscriberMiddleName]Column99 as [SubscriberName]Column100 as [SubscriberTitle]Column56 as [TechComments]Column43 as [TechForm]Column63 as [TechMarkingComments]Column62 as [TechMarkings]Column55 as [TechSignature]Column59 as [TechSignatureDate]Column101 as [TerSubscriberFirstName]Column102 as [TerSubscriberGenderCode]Column103 as [TerSubscriberLastName]Column104 as [TertiaryCarrierCode]Column105 as [TertiaryCarrierID]Column106 as [TertiaryCarrierName]Column27 as [Weight]Column28 as [Weight_Units]Column14 as [ZIP], , Convert(varchar(10),LastModified,101) as LastModified from Answers where UserID=", "ConsentFormHeader": "", "CopayFormHeader": "", "IsDefault": false, "IsLibraryTemplate": "N", "Source": "ACMT", "CreatedDTTM": "2024-05-23 01:20:01.037000", "ModifiedDTTM": null, "IsActive": true, "AssessmentType": null, "AssessmentSubType": null, "IsAging": null, "AgingDays": null, "QuesTemplateCategory": null, "SpecialtyCode": null, "OnDemandWorkflowSetUpVersionID": null, "FolderID": null}}}, {"WorkflowAuditLogId": "44", "AppointmentId": 94, "OrganizationId": 102, "LanguageId": 1, "PatientID": 55, "TypeOfCheckin": null, "UserId": 2, "Status": "In Progress", "WorkflowType": "Receptionist<PERSON><PERSON><PERSON><PERSON>", "LastAnsweredScreenNo": "9", "ScreensAccessed": null, "Channel": null, "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "IPAddress": "************", "DeviceID": "", "SessionID": "jbgozovemdwdtlsqzdo4g2yl", "StartDate": "2024-05-23 13:23:15.877000", "EndDate": "NaT", "IsWorkList": false, "IsWorkFlowCompleted": false, "KioskWorkflowId": 0, "ClientPracticeID": null, "IsMaxID": "Y", "CreatedBy": 2, "CreatedDate": "2024-05-23 03:53:39.540000", "ModifiedBy": NaN, "ModifiedDate": "NaT", "WorkflowSource": "", "ExitScreenNumber": null, "ExitScreenName": null, "DateTimeofExit": null, "IsDocumentSign": null, "KioskWorkflow": null}]}], "TransactionData": {"TransactionNo": "1095391", "OutboundDocuments": [{"OutBoundDocumentID": 101, "OrganizationID": 102, "PatientID": 55, "TransactionNo": "1095391", "WorkflowID": 133, "WorkFlowType": "ReasonforvisitWorkflow", "DocumentTemplateID": 170, "DocumentID": 110, "IsProcessed": "N ", "ProcessMessage": "", "LocationId": 11, "ExternalDocumentID": null, "OutboundAPITranID": null, "CreatedDTTM": "2024-05-23 03:52:43.213000", "ModifiedDTTM": null, "FileName": "", "IsQueueProcessed": null, "IsScoreProcessed": "N"}, {"OutBoundDocumentID": 102, "OrganizationID": 102, "PatientID": 55, "TransactionNo": "1095391", "WorkflowID": 133, "WorkFlowType": "ReasonforvisitWorkflow", "DocumentTemplateID": 169, "DocumentID": 111, "IsProcessed": "N ", "ProcessMessage": "", "LocationId": 11, "ExternalDocumentID": null, "OutboundAPITranID": null, "CreatedDTTM": "2024-05-23 03:52:43.363000", "ModifiedDTTM": null, "FileName": "", "IsQueueProcessed": null, "IsScoreProcessed": "N"}, {"OutBoundDocumentID": 103, "OrganizationID": 102, "PatientID": 55, "TransactionNo": "1095391", "WorkflowID": 133, "WorkFlowType": "ReasonforvisitWorkflow", "DocumentTemplateID": 180, "DocumentID": 112, "IsProcessed": "N ", "ProcessMessage": "", "LocationId": 11, "ExternalDocumentID": null, "OutboundAPITranID": null, "CreatedDTTM": "2024-05-23 03:52:49.523000", "ModifiedDTTM": null, "FileName": "", "IsQueueProcessed": null, "IsScoreProcessed": "N"}, {"OutBoundDocumentID": 104, "OrganizationID": 102, "PatientID": 55, "TransactionNo": "1095391", "WorkflowID": 133, "WorkFlowType": "ReasonforvisitWorkflow", "DocumentTemplateID": 172, "DocumentID": 113, "IsProcessed": "N ", "ProcessMessage": "", "LocationId": 11, "ExternalDocumentID": null, "OutboundAPITranID": null, "CreatedDTTM": "2024-05-23 03:52:49.637000", "ModifiedDTTM": null, "FileName": "", "IsQueueProcessed": null, "IsScoreProcessed": "N"}], "DocumentManagementDetails": [], "APITransactions": []}, "Answers": [{"AnswerTempId": 9529, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2463, "QuestionText": "", "AnswerAction": "modify", "Answer": "50012", "KioskLocationId": 0, "ColumnName": "ZIP", "IsSource": null, "OldAnswer": "50012", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9528, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2461, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPovertyLevelIncomeDeclined", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9527, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2460, "QuestionText": "", "AnswerAction": "", "Answer": "************", "KioskLocationId": 0, "ColumnName": "Secondary_Phone", "IsSource": null, "OldAnswer": "**********", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9526, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2457, "QuestionText": "", "AnswerAction": "Modify", "Answer": "Never smoked", "KioskLocationId": 0, "ColumnName": "Question109", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9525, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "05/05/1995", "KioskLocationId": 0, "ColumnName": "SubscriberDOB", "IsSource": null, "OldAnswer": "05/05/1995", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9524, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecSubscriberLastName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9523, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecSubscriberGenderName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9522, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecSubscriberFirstName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9521, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecPatRelToSubscriber", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9520, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondarySubscriberID", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9519, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryPlanName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9518, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryClaimMemberID", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9517, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryCarrierName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9516, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2454, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryCarrierID", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9515, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "BUSH GEORGE", "KioskLocationId": 0, "ColumnName": "SubscriberName", "IsSource": null, "OldAnswer": "BUSH GEORGE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9514, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "GEORGE", "KioskLocationId": 0, "ColumnName": "SubscriberLastName", "IsSource": null, "OldAnswer": "GEORGE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9513, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "74185", "KioskLocationId": 0, "ColumnName": "SubscriberID", "IsSource": null, "OldAnswer": "74185", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9512, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "Male", "KioskLocationId": 0, "ColumnName": "SubscriberGenderName", "IsSource": null, "OldAnswer": "Male", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9511, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "BUSH", "KioskLocationId": 0, "ColumnName": "SubscriberFirstName", "IsSource": null, "OldAnswer": "BUSH", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9510, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "05/05/1995", "KioskLocationId": 0, "ColumnName": "SubscriberDOB", "IsSource": null, "OldAnswer": "05/05/1995", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9509, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "UNITED HEALTHCARE", "KioskLocationId": 0, "ColumnName": "PlanName", "IsSource": null, "OldAnswer": "UNITED HEALTHCARE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9508, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "Self", "KioskLocationId": 0, "ColumnName": "PatientRelationToSubscriber", "IsSource": null, "OldAnswer": "Self", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9507, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "74185", "KioskLocationId": 0, "ColumnName": "ClaimMemberID", "IsSource": null, "OldAnswer": "74185", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9506, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "United Healthcare", "KioskLocationId": 0, "ColumnName": "CarrierName", "IsSource": null, "OldAnswer": "United Healthcare", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9505, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2453, "QuestionText": "", "AnswerAction": "yes", "Answer": "23581", "KioskLocationId": 0, "ColumnName": "CarrierID", "IsSource": null, "OldAnswer": "23581", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9504, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2452, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPovertyLevelFamilySizeDeclined", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9503, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2451, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "<PERSON><PERSON><PERSON><PERSON>an", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9502, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2448, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "pharinstru", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9501, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2447, "QuestionText": "", "AnswerAction": "", "Answer": "352452369", "KioskLocationId": 0, "ColumnName": "SSN", "IsSource": null, "OldAnswer": "352452369", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9499, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2444, "QuestionText": "", "AnswerAction": "Yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "telemeddoc_IMP20230807054526703CM", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9497, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2441, "QuestionText": "", "AnswerAction": "yes", "Answer": "74185", "KioskLocationId": 0, "ColumnName": "SubscriberID", "IsSource": null, "OldAnswer": "74185", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9496, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2440, "QuestionText": "", "AnswerAction": "yes", "Answer": "BUSH GEORGE", "KioskLocationId": 0, "ColumnName": "SubscriberName", "IsSource": null, "OldAnswer": "BUSH GEORGE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9495, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2440, "QuestionText": "", "AnswerAction": "yes", "Answer": "GEORGE", "KioskLocationId": 0, "ColumnName": "SubscriberLastName", "IsSource": null, "OldAnswer": "GEORGE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9494, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2440, "QuestionText": "", "AnswerAction": "yes", "Answer": "Male", "KioskLocationId": 0, "ColumnName": "SubscriberGenderName", "IsSource": null, "OldAnswer": "Male", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9493, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2440, "QuestionText": "", "AnswerAction": "yes", "Answer": "BUSH", "KioskLocationId": 0, "ColumnName": "SubscriberFirstName", "IsSource": null, "OldAnswer": "BUSH", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9492, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2440, "QuestionText": "", "AnswerAction": "", "Answer": "05/05/1995", "KioskLocationId": 0, "ColumnName": "SubscriberDOB", "IsSource": null, "OldAnswer": "05/05/1995", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9490, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2432, "QuestionText": "", "AnswerAction": "", "Answer": "Don't know", "KioskLocationId": 0, "ColumnName": "SexualOrientation", "IsSource": null, "OldAnswer": "Don't know", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9489, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2430, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecSubscriberFirstName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9488, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2429, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryInsImageFront", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9487, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2427, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryClaimMemberID", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9486, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2426, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "SecondaryCarrierName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:06:54.760000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9483, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2423, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientSchoolBasedHealthcenter", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9482, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2420, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question347", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9481, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2413, "QuestionText": "", "AnswerAction": "modify", "Answer": "LYNN1 ANDERSON1", "KioskLocationId": 0, "ColumnName": "ReferProviderFullName", "IsSource": null, "OldAnswer": "LYNN1 ANDERSON1", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9480, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2412, "QuestionText": "", "AnswerAction": "", "Answer": "<PERSON><PERSON>", "KioskLocationId": 0, "ColumnName": "Race", "IsSource": null, "OldAnswer": "<PERSON><PERSON>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9479, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2411, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "Rabies", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9478, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2410, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPublicHousing", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9477, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2409, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question263", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9476, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2407, "QuestionText": "", "AnswerAction": "", "Answer": "Chinese", "KioskLocationId": 0, "ColumnName": "PrimaryLanguage", "IsSource": null, "OldAnswer": "Chinese", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9474, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2405, "QuestionText": "", "AnswerAction": "modify", "Answer": "LYNN1 ANDERSON1", "KioskLocationId": 0, "ColumnName": "PCPProviderFullName", "IsSource": null, "OldAnswer": "LYNN1 ANDERSON1", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9473, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2404, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "Premature", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9472, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2403, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PreferredContactMethod", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9471, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2400, "QuestionText": "", "AnswerAction": "Modify", "Answer": "Knee Replacement", "KioskLocationId": 0, "ColumnName": "Question202", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9470, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2399, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_Qst", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9469, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2398, "QuestionText": "", "AnswerAction": "Yes", "Answer": "Advil$!RabAvert (PF)$!Re-Gen oral liquid$!Sabril 500 mg tablet", "KioskLocationId": 0, "ColumnName": "AuthPersonThreeDOB", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9468, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2397, "QuestionText": "", "AnswerAction": "Yes", "Answer": "ALAVERT D-12 ALLERGY-SINUS$$OD$@$#RE$@none@#SE$@none$!CABBAGE$$OD$@$#RE$@none@#SE$@none$!RABBIT$$OD$@$#RE$@none@#SE$@none$!ALA-CORT$$OD$@$#RE$@None@#SE$@None", "KioskLocationId": 0, "ColumnName": "MedHistALGY", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9467, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2396, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "drivinglicense", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9466, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2395, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "Weight", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9465, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2394, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "Height", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9464, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2389, "QuestionText": "", "AnswerAction": "yes", "Answer": "UNITED HEALTHCARE", "KioskLocationId": 0, "ColumnName": "PlanName", "IsSource": null, "OldAnswer": "UNITED HEALTHCARE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:06:53.823000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:36AM"}, {"AnswerTempId": 9460, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2380, "QuestionText": "", "AnswerAction": "Yes", "Answer": "1", "KioskLocationId": 0, "ColumnName": "PayAtDesk", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 03:53:05.293000", "Source": "Inbound", "ModifiedDTTM": null}, {"AnswerTempId": 9452, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2374, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientIDImage", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9449, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2366, "QuestionText": "", "AnswerAction": "Modify", "Answer": "Sea Sickness$!High Blood Pressure$!Anxiety Disorder$!Anemia", "KioskLocationId": 0, "ColumnName": "pmh", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9446, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2359, "QuestionText": "", "AnswerAction": "Yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "INTFPrivacyNotice_IMP20230905010632703CM", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9443, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2355, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question262", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9440, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2343, "QuestionText": "", "AnswerAction": "Yes", "Answer": "Cough$!Corneal abrasion$!Asthma$!Fever with chills$!Anemia", "KioskLocationId": 0, "ColumnName": "ProblemMigraine", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9439, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2342, "QuestionText": "", "AnswerAction": "Modify", "Answer": "Brother", "KioskLocationId": 0, "ColumnName": "Question220", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9438, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2339, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "InsuranceImage", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9437, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2337, "QuestionText": "", "AnswerAction": "Modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPovertyLevelIncomeperPayPeriod", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9436, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2336, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPovertyLevelIncomePayPeriod", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9435, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2335, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_Qst2", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9434, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2334, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PD", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9433, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2333, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_qst4", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9432, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2331, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_Qst", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9431, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2325, "QuestionText": "", "AnswerAction": "Modify", "Answer": "2 packs per week", "KioskLocationId": 0, "ColumnName": "smokepac", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9430, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2323, "QuestionText": "", "AnswerAction": "Modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientHomelessType", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9429, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2322, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientHomeless", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9428, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2320, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "HIV", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9427, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2318, "QuestionText": "", "AnswerAction": "", "Answer": "Father$!Mother", "KioskLocationId": 0, "ColumnName": "Question229", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9426, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2312, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "ParentGuardianLastName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9425, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2311, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "ParentGuardianFirst<PERSON>ame", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9424, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2310, "QuestionText": "", "AnswerAction": "modify", "Answer": "50012", "KioskLocationId": 0, "ColumnName": "GuarantorZip", "IsSource": null, "OldAnswer": "50012", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9423, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2309, "QuestionText": "", "AnswerAction": "", "Answer": "Self", "KioskLocationId": 0, "ColumnName": "GuarantorRelationShip", "IsSource": null, "OldAnswer": "Self", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9422, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2308, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "GuarantorMobilePhone", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9421, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2307, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "GuarantorMiddleName", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9420, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2306, "QuestionText": "", "AnswerAction": "modify", "Answer": "<PERSON>", "KioskLocationId": 0, "ColumnName": "GuarantorLastname", "IsSource": null, "OldAnswer": "<PERSON>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9419, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2305, "QuestionText": "", "AnswerAction": "modify", "Answer": "9846524700", "KioskLocationId": 0, "ColumnName": "GuarantorHomePhone", "IsSource": null, "OldAnswer": "9846524700", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9418, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2304, "QuestionText": "", "AnswerAction": "modify", "Answer": "<PERSON>", "KioskLocationId": 0, "ColumnName": "GuarantorFirstName", "IsSource": null, "OldAnswer": "<PERSON>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9417, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2303, "QuestionText": "", "AnswerAction": "modify", "Answer": "<EMAIL>", "KioskLocationId": 0, "ColumnName": "GuarantorEmail", "IsSource": null, "OldAnswer": "<EMAIL>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9415, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2301, "QuestionText": "", "AnswerAction": "modify", "Answer": "AURORA", "KioskLocationId": 0, "ColumnName": "GuarantorCity", "IsSource": null, "OldAnswer": "AURORA", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9414, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2300, "QuestionText": "", "AnswerAction": "modify", "Answer": "<PERSON>", "KioskLocationId": 0, "ColumnName": "GuarantorAddress2", "IsSource": null, "OldAnswer": "<PERSON>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9413, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2299, "QuestionText": "", "AnswerAction": "modify", "Answer": "25/2, Laned", "KioskLocationId": 0, "ColumnName": "GuarantorAddress1", "IsSource": null, "OldAnswer": "25/2, Laned", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9412, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2298, "QuestionText": "", "AnswerAction": "", "Answer": "Identifies as Male", "KioskLocationId": 0, "ColumnName": "GenderIdentity", "IsSource": null, "OldAnswer": "Identifies as Male", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9411, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2297, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question346", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9409, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2293, "QuestionText": "", "AnswerAction": "Yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "FinancialRespDoc_IMP20230905010632703CM", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9408, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2290, "QuestionText": "", "AnswerAction": "Modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPovertyLevelFamilySize", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9406, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2288, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question233", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9404, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2283, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "eyeros", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9403, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2282, "QuestionText": "", "AnswerAction": "", "Answer": "Cuban", "KioskLocationId": 0, "ColumnName": "Ethnicity", "IsSource": null, "OldAnswer": "Cuban", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9402, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2280, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "endoros", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9401, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2277, "QuestionText": "", "AnswerAction": "yes", "Answer": "EmployerID$@368$#EmployerName$@BREVARD COUNTY SCHOOL$#Address1$@2700 JUDGE FRAN JAMISON$#State$@FL$#City$@MELBOURNE$#Zip$@32940", "KioskLocationId": 0, "ColumnName": "EmployerDetails", "IsSource": null, "OldAnswer": "EmployerID$@368$#EmployerName$@BREVARD COUNTY SCHOOL$#Address1$@2700 JUDGE FRAN JAMISON$#State$@FL$#City$@MELBOURNE$#Zip$@32940", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9400, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2274, "QuestionText": "", "AnswerAction": "modify", "Answer": "", "KioskLocationId": 0, "ColumnName": "NextKinZip", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9399, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2272, "QuestionText": "", "AnswerAction": "modify", "Answer": "FRIEND", "KioskLocationId": 0, "ColumnName": "NextKinRelationship", "IsSource": null, "OldAnswer": "FRIEND", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9398, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2270, "QuestionText": "", "AnswerAction": "modify", "Answer": "6584235611", "KioskLocationId": 0, "ColumnName": "NextKinMobilePhone", "IsSource": null, "OldAnswer": "6584235611", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9397, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2267, "QuestionText": "", "AnswerAction": "modify", "Answer": "6584235611", "KioskLocationId": 0, "ColumnName": "NextKinPhone", "IsSource": null, "OldAnswer": "6584235611", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9395, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2261, "QuestionText": "", "AnswerAction": "modify", "Answer": "PAUL", "KioskLocationId": 0, "ColumnName": "NextKinLastName", "IsSource": null, "OldAnswer": "PAUL", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9394, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2260, "QuestionText": "", "AnswerAction": "", "Answer": "<EMAIL>", "KioskLocationId": 0, "ColumnName": "Email", "IsSource": null, "OldAnswer": "<EMAIL>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9392, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2255, "QuestionText": "", "AnswerAction": "Modify", "Answer": "Formerly", "KioskLocationId": 0, "ColumnName": "TobaccoSmokingStatus", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9391, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2253, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_qst5", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9390, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2252, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question167", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9389, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2251, "QuestionText": "", "AnswerAction": "yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "DEPERSS", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9388, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2250, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "CANPMH", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9387, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2249, "QuestionText": "", "AnswerAction": "yes", "Answer": "PharmacyID$@11311711$#PharmacyName$@Carl  Waldman$#Address1$@1635 N George Mason Dr Ste 420$#State$@VA$#City$@Arlington$#Zip$@22205$#Phone$@7035364000", "KioskLocationId": 0, "ColumnName": "PharmacyDetails", "IsSource": null, "OldAnswer": "PharmacyID$@11311711$#PharmacyName$@Carl  Waldman$#Address1$@1635 N George Mason Dr Ste 420$#State$@VA$#City$@Arlington$#Zip$@22205$#Phone$@7035364000", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9384, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "modify", "Answer": "50012", "KioskLocationId": 0, "ColumnName": "ZIP", "IsSource": null, "OldAnswer": "50012", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9383, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "", "Answer": "DE", "KioskLocationId": 0, "ColumnName": "State", "IsSource": null, "OldAnswer": "DE", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9382, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "", "Answer": "USA", "KioskLocationId": 0, "ColumnName": "Country", "IsSource": null, "OldAnswer": "USA", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9381, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "", "Answer": "AURORA", "KioskLocationId": 0, "ColumnName": "City", "IsSource": null, "OldAnswer": "AURORA", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9380, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "", "Answer": "<PERSON>", "KioskLocationId": 0, "ColumnName": "Address2", "IsSource": null, "OldAnswer": "<PERSON>", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9379, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2243, "QuestionText": "", "AnswerAction": "", "Answer": "25/2, Laned", "KioskLocationId": 0, "ColumnName": "Address1", "IsSource": null, "OldAnswer": "25/2, Laned", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9377, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2240, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientPrePay", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9376, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2240, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientBalancePaid", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9375, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2240, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "CoPay", "IsSource": null, "OldAnswer": "0", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9373, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2238, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question258", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9371, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2235, "QuestionText": "", "AnswerAction": "", "Answer": "************", "KioskLocationId": 0, "ColumnName": "Mobile_Phone", "IsSource": null, "OldAnswer": "6542563123", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9369, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2233, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "Question348", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9368, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2231, "QuestionText": "", "AnswerAction": "Yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_Doc", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9367, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2229, "QuestionText": "", "AnswerAction": "Yes", "Answer": "", "KioskLocationId": 0, "ColumnName": "INTFAssignOfBenefits_IMP20230905010632703CM", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": null, "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9365, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2226, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "COVID_qst1", "IsSource": null, "OldAnswer": null, "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}, {"AnswerTempId": 9363, "OrganizationId": 102, "DeptId": 0, "AppNo": "1095391", "UserID": 55, "QuestionId": 2221, "QuestionText": "", "AnswerAction": "", "Answer": "", "KioskLocationId": 0, "ColumnName": "PatientAgriculturalWorker", "IsSource": null, "OldAnswer": "", "OldAnswerAction": null, "IsRecepAccepted": false, "IsQuestionAnswered": "Y", "ModifyById": 55, "DateCreated": "2024-05-23 13:23:05.517000", "Source": "Inbound", "ModifiedDTTM": "May 23 2024  3:53AM"}], "Summary": {"TotalSupportRecords": 1, "TotalWorkflowAudits": 2, "TotalKioskWorkflows": 1, "TotalQuestionTemplates": 1, "TotalOutboundDocuments": 4, "TotalDocumentManagementDetails": 0, "TotalAPITransactions": 0, "TotalAnswers": 129}}]}}