[project]
name = "supportagentv1"
version = "0.1.0"
description = "AI-powered IT Support Agent using LangGraph"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.1.0",
    "langchain>=0.1.0",
    "langchain-openai>=0.0.5",
    "langchain-community>=0.0.10",
    "sqlalchemy>=2.0.0",
    "sqlite3-api>=2.0.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "rich>=13.7.0",
    "streamlit>=1.29.0",
    "pandas>=2.1.0",
    "langchain-core>=0.3.68",
    "google-generativeai>=0.8.5",
    "plotly>=6.2.0",
    "pyodbc>=5.2.0",
]
